export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  canonical?: string;
  noindex?: boolean;
  structuredData?: object;
}

export const defaultSEO: SEOData = {
  title: "AiLex - AI Legal Associate for Solo Practitioners & Small Firms",
  description:
    "Transform your legal practice with AiL<PERSON>, the AI-powered assistant that automates client intake, legal research, and document drafting. Built for solo practitioners and small law firms. Start your free trial today.",
  keywords:
    "AI legal assistant, legal research, solo practitioner, small law firm, legal technology, document drafting, case management, legal AI",
  ogTitle: "AiLex - Your AI Legal Associate",
  ogDescription:
    "Transform your legal practice with AI-powered automation. Streamline client intake, research, and document drafting for solo practitioners and small firms.",
  ogImage: "/assets/ailex-og-image.webp",
  ogType: "website",
  twitterCard: "summary_large_image",
  twitterTitle: "AiLex - AI Legal Associate",
  twitterDescription:
    "Transform your legal practice with AI-powered automation for solo practitioners and small firms.",
  twitterImage: "/assets/ailex-twitter-image.webp",
  canonical: "https://ailexlaw.com",
};

export const pageSEO: Record<string, SEOData> = {
  home: {
    title: "AiLex - AI Legal Associate for Solo Practitioners & Small Firms",
    description:
      "Transform your legal practice with AiLex, the AI-powered assistant that automates client intake, legal research, and document drafting. Built for solo practitioners and small law firms. Start your free trial today.",
    keywords:
      "AI legal assistant, legal research, solo practitioner, small law firm, legal technology, document drafting, case management, legal AI",
    ogTitle: "AiLex - Your AI Legal Associate",
    ogDescription:
      "Transform your legal practice with AI-powered automation. Streamline client intake, research, and document drafting for solo practitioners and small firms.",
    canonical: "https://ailexlaw.com",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      name: "AiLex",
      description:
        "AI-powered legal assistant for solo practitioners and small law firms",
      applicationCategory: "LegalTech",
      operatingSystem: "Web",
      offers: {
        "@type": "Offer",
        price: "99",
        priceCurrency: "USD",
        priceValidUntil: "2025-12-31",
      },
      provider: {
        "@type": "Organization",
        name: "AiLex Law",
        url: "https://ailex.law",
      },
    },
  },
  blog: {
    title: "Legal Insights & Resources for Solo Practitioners | AiLex Blog",
    description:
      "Expert legal insights, practice management tips, and AI technology guides for solo practitioners and small law firms. Stay updated with the latest legal tech trends.",
    keywords:
      "legal blog, solo practitioner tips, legal technology, practice management, legal research techniques",
    ogTitle: "AiLex Blog - Legal Insights & Resources",
    ogDescription:
      "Expert legal insights and practice management tips for solo practitioners and small law firms.",
    canonical: "https://ailexlaw.com/blog",
  },
  login: {
    title: "Request Early Access to AiLex - AI Legal Assistant",
    description:
      "Get early access to AiLex, the AI-powered legal assistant designed for solo practitioners and small law firms. Join our waitlist for exclusive beta access.",
    keywords:
      "AiLex early access, legal AI beta, solo practitioner software, legal assistant signup",
    ogTitle: "Request Early Access to AiLex",
    ogDescription:
      "Get early access to the AI legal assistant built for solo practitioners.",
    canonical: "https://ailexlaw.com/login",
    noindex: true,
  },
  demo: {
    title: "AiLex Demo - See AI Legal Assistant in Action",
    description:
      "Watch a 90-second demo of AiLex, the AI-powered legal assistant that handles research, drafts documents, and manages client intake for solo practitioners.",
    keywords:
      "AiLex demo, legal AI demonstration, legal assistant software, AI legal research, legal document drafting",
    ogTitle: "AiLex Demo - AI Legal Assistant in Action",
    ogDescription:
      "See how AiLex transforms legal practice with AI-powered research, document drafting, and client management.",
    canonical: "https://ailexlaw.com/demo",
  },
  securityWhitepaper: {
    title: "AiLex Security Overview - Legal-Grade Data Protection",
    description:
      "Learn about AiLex's enterprise-grade security measures, encryption standards, and compliance features designed specifically for legal professionals.",
    keywords:
      "legal software security, attorney client privilege, legal data protection, HIPAA compliance, legal tech security",
    ogTitle: "AiLex Security Overview",
    ogDescription:
      "Enterprise-grade security measures designed specifically for legal professionals.",
    canonical: "https://ailex.law/security-whitepaper",
  },
  privacyPolicy: {
    title: "Privacy Policy | AiLex Law - AI Legal Assistant",
    description:
      "AiLex Law's privacy policy detailing how we collect, use, and protect your personal information in compliance with GDPR, CCPA, and other privacy regulations.",
    keywords:
      "privacy policy, data protection, GDPR, CCPA, legal privacy, AI legal assistant, attorney privacy",
    ogTitle: "AiLex Privacy Policy",
    ogDescription:
      "Learn how AiLex protects your personal information and complies with privacy regulations.",
    canonical: "https://ailexlaw.com/privacy-policy",
  },
};

export const stateSEO = {
  TX: {
    title: "AI Legal Assistant for Texas Lawyers | AiLex",
    description:
      "Transform your Texas law practice with AiLex. Automate client intake, legal research, and document drafting. Built for solo practitioners and small law firms in Texas. Start your free trial.",
    keywords:
      "Texas legal assistant, AI lawyer Texas, legal technology Texas, solo practitioner Texas, small law firm Texas",
    ogTitle: "AiLex - AI Legal Assistant for Texas Lawyers",
    ogDescription:
      "AI-powered legal assistant specifically designed for Texas attorneys and law firms.",
    canonical: "https://ailexlaw.com/tx",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "ProfessionalService",
      name: "AiLex for Texas Lawyers",
      description: "AI legal assistant for Texas attorneys",
      areaServed: {
        "@type": "State",
        name: "Texas",
      },
    },
  },
  FL: {
    title: "AI Legal Assistant for Florida Lawyers | AiLex",
    description:
      "Transform your Florida law practice with AiLex. Automate client intake, legal research, and document drafting. Built for solo practitioners and small law firms in Florida. Start your free trial.",
    keywords:
      "Florida legal assistant, AI lawyer Florida, legal technology Florida, solo practitioner Florida, small law firm Florida",
    ogTitle: "AiLex - AI Legal Assistant for Florida Lawyers",
    ogDescription:
      "AI-powered legal assistant specifically designed for Florida attorneys and law firms.",
    canonical: "https://ailexlaw.com/fl",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "ProfessionalService",
      name: "AiLex for Florida Lawyers",
      description: "AI legal assistant for Florida attorneys",
      areaServed: {
        "@type": "State",
        name: "Florida",
      },
    },
  },
  NY: {
    title: "AI Legal Assistant for New York Lawyers | AiLex",
    description:
      "Transform your New York law practice with AiLex. Automate client intake, legal research, and document drafting. Built for solo practitioners and small law firms in New York. Start your free trial.",
    keywords:
      "New York legal assistant, AI lawyer New York, legal technology New York, solo practitioner New York, small law firm New York",
    ogTitle: "AiLex - AI Legal Assistant for New York Lawyers",
    ogDescription:
      "AI-powered legal assistant specifically designed for New York attorneys and law firms.",
    canonical: "https://ailexlaw.com/ny",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "ProfessionalService",
      name: "AiLex for New York Lawyers",
      description: "AI legal assistant for New York attorneys",
      areaServed: {
        "@type": "State",
        name: "New York",
      },
    },
  },
};

export function updateMetaTags(seoData: SEOData): void {
  // Update title
  document.title = seoData.title;

  // Update or create meta tags
  updateMetaTag("description", seoData.description);

  if (seoData.keywords) {
    updateMetaTag("keywords", seoData.keywords);
  }

  // Open Graph tags
  updateMetaTag("og:title", seoData.ogTitle || seoData.title, "property");
  updateMetaTag(
    "og:description",
    seoData.ogDescription || seoData.description,
    "property"
  );
  updateMetaTag("og:type", seoData.ogType || "website", "property");
  updateMetaTag(
    "og:url",
    seoData.canonical || window.location.href,
    "property"
  );

  if (seoData.ogImage) {
    updateMetaTag("og:image", seoData.ogImage, "property");
  }

  // Twitter Card tags
  updateMetaTag("twitter:card", seoData.twitterCard || "summary_large_image");
  updateMetaTag("twitter:title", seoData.twitterTitle || seoData.title);
  updateMetaTag(
    "twitter:description",
    seoData.twitterDescription || seoData.description
  );

  if (seoData.twitterImage) {
    updateMetaTag("twitter:image", seoData.twitterImage);
  }

  // Canonical URL
  if (seoData.canonical) {
    updateLinkTag("canonical", seoData.canonical);
  }

  // Robots meta tag
  if (seoData.noindex) {
    updateMetaTag("robots", "noindex, nofollow");
  } else {
    updateMetaTag("robots", "index, follow");
  }

  // Structured data
  if (seoData.structuredData) {
    updateStructuredData(seoData.structuredData);
  }
}

function updateMetaTag(
  name: string,
  content: string,
  attribute: string = "name"
): void {
  let element = document.querySelector(
    `meta[${attribute}="${name}"]`
  ) as HTMLMetaElement;

  if (!element) {
    element = document.createElement("meta");
    element.setAttribute(attribute, name);
    document.head.appendChild(element);
  }

  element.content = content;
}

function updateLinkTag(rel: string, href: string): void {
  let element = document.querySelector(`link[rel="${rel}"]`) as HTMLLinkElement;

  if (!element) {
    element = document.createElement("link");
    element.rel = rel;
    document.head.appendChild(element);
  }

  element.href = href;
}

function updateStructuredData(data: object): void {
  // Remove existing structured data
  const existing = document.querySelector('script[type="application/ld+json"]');
  if (existing) {
    existing.remove();
  }

  // Add new structured data
  const script = document.createElement("script");
  script.type = "application/ld+json";
  script.textContent = JSON.stringify(data);
  document.head.appendChild(script);
}
